import 'package:equatable/equatable.dart';
import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/user_info_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/user_info_entity.g.dart';

@JsonSerializable()
class UserInfoEntity extends Equatable {
  late int agentId = 0;
  late String agentUserNo = '';
  late String userNo = '';
  late int userType = 0;
  late int faceId = 0;
  late String nickName = '';
  late String email = '';
  late String phoneNo = '';
  late int gender = 0;
  dynamic birthday;
  late String vipTitle = '';
  late int vipLevel = 0;
  late bool hasFundPwd = false;

  @JSONField(name: "movieView")
  late bool tiktokTabVisible = true;
  late double integral;
  late String lastLoginTime = '';
  late int enableTransferAmount = 0;
  late String realName = '';
  late int phoneStatus = 0;
  late String language = '';

  UserInfoEntity();

  factory UserInfoEntity.fromJson(Map<String, dynamic> json) => $UserInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserInfoEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  UserInfoEntity copyWith({
    int? agentId,
    String? agentUserNo,
    String? userNo,
    int? userType,
    int? faceId,
    String? nickName,
    String? email,
    String? phoneNo,
    int? gender,
    dynamic birthday,
    String? vipTitle,
    int? vipLevel,
    bool? hasFundPwd,
    bool? tiktokTabVisible,
    double? integral,
    String? lastLoginTime,
    int? enableTransferAmount,
    String? realName,
    int? phoneStatus,
    String? language,
  }) {
    final copy = UserInfoEntity();
    copy.agentId = agentId ?? this.agentId;
    copy.agentUserNo = agentUserNo ?? this.agentUserNo;
    copy.userNo = userNo ?? this.userNo;
    copy.userType = userType ?? this.userType;
    copy.faceId = faceId ?? this.faceId;
    copy.nickName = nickName ?? this.nickName;
    copy.email = email ?? this.email;
    copy.phoneNo = phoneNo ?? this.phoneNo;
    copy.gender = gender ?? this.gender;
    copy.birthday = birthday ?? this.birthday;
    copy.vipTitle = vipTitle ?? this.vipTitle;
    copy.vipLevel = vipLevel ?? this.vipLevel;
    copy.hasFundPwd = hasFundPwd ?? this.hasFundPwd;
    copy.tiktokTabVisible = tiktokTabVisible ?? this.tiktokTabVisible;
    copy.integral = integral ?? this.integral;
    copy.lastLoginTime = lastLoginTime ?? this.lastLoginTime;
    copy.enableTransferAmount = enableTransferAmount ?? this.enableTransferAmount;
    copy.realName = realName ?? this.realName;
    copy.phoneStatus = phoneStatus ?? this.phoneStatus;
    copy.language = language ?? this.language;
    return copy;
  }

  @override
  List<Object?> get props => [
        agentId,
        agentUserNo,
        userNo,
        userType,
        faceId,
        nickName,
        email,
        phoneNo,
        gender,
        birthday,
        vipLevel,
        vipTitle,
        hasFundPwd,
        tiktokTabVisible,
        integral,
        lastLoginTime,
        enableTransferAmount,
        realName,
        phoneStatus,
        language,
      ];
}

@JsonSerializable()
class UserBalanceEntity {
  late double accountMoney;
  late double lockMoney;

  UserBalanceEntity();

  factory UserBalanceEntity.fromJson(Map<String, dynamic> json) => $UserBalanceEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserBalanceEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
