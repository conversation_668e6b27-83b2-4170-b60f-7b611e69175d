import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/game_v2_entity.g.dart';
import 'package:easy_localization/easy_localization.dart';

export 'package:wd/generated/json/game_v2_entity.g.dart';

@JsonSerializable()
class GameTypeV2ListEntity {
  late List<GameTypeV2> list = [];

  GameTypeV2ListEntity();

  factory GameTypeV2ListEntity.fromJson(Map<String, dynamic> json) => $GameTypeV2ListEntityFromJson(json);

  Map<String, dynamic> toJson() => $GameTypeV2ListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GameTypeV2 {
  String code = ''; // 类型Code
  String name = ''; // 类型名称
  @JSONField(name: "plats")
  List<GamePlatformV2> data = []; // 场馆
  List<GameV2> games = []; // 游戏(仅适用于热门游戏)
  String classIcon = ''; // 类型图标
  @JSONField(name: "gameType")
  int type = 0; // 游戏类型,1.三方游戏 2.彩票游戏 3.热门
  /// 1=>场馆 0=>游戏 是否可以直接打开游戏,否则使用场馆 
  int isGame = 0;

  double sectionHeight = 0; // 区块高度，用于点击切换scrollview.inset

  bool get isHot => type == 3;

  GameTypeV2();

  GameTypeV2 clone() {
    return GameTypeV2.fromJson(toJson());
  }

  factory GameTypeV2.fromJson(Map<String, dynamic> json) => $GameTypeV2FromJson(json);

  Map<String, dynamic> toJson() => $GameTypeV2ToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GamePlatformV2 {
  @JSONField(name: "platformId")
  int id = 0; // 平台ID
  @JSONField(name: "platformName")
  String name = ''; // 平台名称
  @JSONField(name: "platformCode")
  String code = ''; // 平台Code
  String gameClassCode = ''; // 游戏大类Code
  @JSONField(name: "icon")
  String logoUrl = ''; // 场馆图标
  @JSONField(name: "picture")
  String mainImgUrl = ''; // 场馆图片
  int isGame = 0; // 场馆是否为游戏 0.否 1.是
  int isAegis = 0; // 维护状态 0否 1是
  @JSONField(name: "gameType")
  int type = 0; // 游戏类型,1.三方游戏 2.彩票游戏 3.热门

  GamePlatformV2();

  static GamePlatformV2 createAllGame(String gameClassCode) {
    var allGamePlatformV2 = GamePlatformV2();
    allGamePlatformV2.gameClassCode = gameClassCode;
    allGamePlatformV2.code = GamePlatformV2.allGameCode;
    allGamePlatformV2.name = 'all_game'.tr();
    return allGamePlatformV2;
  }

  static String allGameCode = 'ALL GAME CODE';

  GamePlatformV2 copyWith({
    int? id,
    String? name,
    String? code,
    String? gameClassCode,
    String? logoUrl,
    String? mainImgUrl,
    int? isGame,
    int? isAegis,
    int? type,
  }) {
    return GamePlatformV2()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..code = code ?? this.code
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..logoUrl = logoUrl ?? this.logoUrl
      ..mainImgUrl = mainImgUrl ?? this.mainImgUrl
      ..isGame = isGame ?? this.isGame
      ..isAegis = isAegis ?? this.isAegis
      ..type = type ?? this.type;
  }

  factory GamePlatformV2.fromJson(Map<String, dynamic> json) => $GamePlatformV2FromJson(json);

  Map<String, dynamic> toJson() => $GamePlatformV2ToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GameV2ListEntity {
  late List<GameV2> list = [];

  GameV2ListEntity();

  factory GameV2ListEntity.fromJson(Map<String, dynamic> json) => $GameV2ListEntityFromJson(json);

  Map<String, dynamic> toJson() => $GameV2ListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GameV2 extends Equatable {
  int id = 0;
  int platformId = 0;
  bool isSavour = false; // 是否已收藏
  String gameClassCode = '';

  @JSONField(name: "gameCode")
  String code = '';

  @JSONField(name: "gameName")
  String name = '';

  @JSONField(name: "iconUrl")
  String mainImgUrl = '';

  @JSONField(name: "gameType")
  int type = 0;

  String platIcon = '';

  GameV2();

  GameV2 copyWith({
    int? id,
    String? gameClassCode,
    String? code,
    String? name,
    int? platformId,
    String? mainImgUrl,
    int? type,
    bool? savour,
  }) {
    return GameV2()
      ..id = id ?? this.id
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..platformId = platformId ?? this.platformId
      ..mainImgUrl = mainImgUrl ?? this.mainImgUrl
      ..type = type ?? this.type
      ..isSavour = savour ?? isSavour;
  }

  factory GameV2.fromJson(Map<String, dynamic> json) => $GameV2FromJson(json);

  Map<String, dynamic> toJson() => $GameV2ToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  @override
  List<Object?> get props => [
        id,
        gameClassCode,
        code,
        name,
        platformId,
        mainImgUrl,
        type,
        isSavour,
      ];
}
