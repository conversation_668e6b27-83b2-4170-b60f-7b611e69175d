import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class CommonTable extends StatelessWidget {
  final List<CommonTableColumn> columns;
  final List<List<String>> data;
  final double headerHeight;
  final double rowHeight;
  final double borderRadius;

  const CommonTable({
    super.key,
    required this.columns,
    required this.data,
    this.headerHeight = 33,
    this.rowHeight = 33,
    this.borderRadius = 6,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: const Color(0xFF030303),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildHeader(context),
          ...data.asMap().entries.map((entry) {
            final index = entry.key;
            final rowData = entry.value;
            final isLast = index == data.length - 1;
            return _buildDataRow(context, rowData, isLast);
          }),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: headerHeight,
      decoration: BoxDecoration(
        color: const Color(0xFFFFD038),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(borderRadius),
          topRight: Radius.circular(borderRadius),
        ),
      ),
      child: Row(
        children: columns.asMap().entries.map((entry) {
          final index = entry.key;
          final column = entry.value;
          final isLast = index == columns.length - 1;

          return _buildHeaderCell(
            context,
            column,
            isLast,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, CommonTableColumn column, bool isLast) {
    return Expanded(
      flex: column.flex ?? 1,
      child: Container(
        height: headerHeight,
        decoration: BoxDecoration(
          border: Border(
            right: isLast
                ? BorderSide.none
                : const BorderSide(
                    color: Color(0xFF030303),
                    width: 1,
                  ),
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 6.gw),
        child: Align(
          alignment: column.alignment == TextAlign.center ? Alignment.center : Alignment.centerLeft,
          child: Text(
            column.title,
            style: context.textTheme.regular.fs13.w500.copyWith(
              color: const Color(0xFF030303),
            ),
            textAlign: TextAlign.left,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  Widget _buildDataRow(BuildContext context, List<String> rowData, bool isLast) {
    return Container(
      height: rowHeight,
      decoration: BoxDecoration(
        color: const Color(0xFF212121),
        borderRadius: isLast
            ? BorderRadius.only(
                bottomLeft: Radius.circular(borderRadius),
                bottomRight: Radius.circular(borderRadius),
              )
            : null,
      ),
      child: Row(
        children: rowData.asMap().entries.map((entry) {
          final index = entry.key;
          final cellData = entry.value;
          final isLastCell = index == rowData.length - 1;
          final isFirstCell = index == 0;
          final column = index < columns.length ? columns[index] : null;

          return _buildDataCell(
            context,
            cellData,
            column,
            isLastCell,
            isFirstCell,
            isLast,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDataCell(
    BuildContext context,
    String text,
    CommonTableColumn? column,
    bool isLastCell,
    bool isFirstCell,
    bool isLastRow,
  ) {
    final style = column?.style;

    return Expanded(
      flex: column?.flex ?? 1,
      child: Container(
        height: rowHeight,
        decoration: BoxDecoration(
          color: style?.backgroundColor,
          border: Border(
            right: isLastCell
                ? BorderSide.none
                : const BorderSide(
                    color: Color(0xFF161616),
                    width: 1,
                  ),
            bottom: isLastRow
                ? BorderSide.none
                : const BorderSide(
                    color: Color(0xFF161616),
                    width: 1,
                  ),
          ),
          borderRadius: isLastRow
              ? BorderRadius.only(
                  bottomLeft: isFirstCell ? Radius.circular(borderRadius) : Radius.zero,
                  bottomRight: isLastCell ? Radius.circular(borderRadius) : Radius.zero,
                )
              : null,
        ),
        child: Align(
          alignment: column?.alignment == TextAlign.center ? Alignment.center : Alignment.centerLeft,
          child: _buildCellContent(context, text, style),
        ),
      ),
    );
  }

  Widget _buildCellContent(BuildContext context, String text, CommonTableColumnStyle? style) {
    if (style?.showBadge == true) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 2.gw),
        decoration: BoxDecoration(
          color: style?.badgeColor ?? const Color(0xFF6F5502),
          borderRadius: BorderRadius.circular(style?.badgeRadius ?? 15),
        ),
        child: Text(
          text,
          style: context.textTheme.regular.fs13.copyWith(
            color: style?.badgeTextColor ?? const Color(0xFFFFD038),
            fontWeight: FontWeight.w400,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      );
    }

    return Text(
      text,
      style: context.textTheme.regular.fs13.copyWith(
        color: style?.textColor ?? const Color(0xFFB4B3B3),
        fontWeight: FontWeight.w400,
      ),
      textAlign: TextAlign.left,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }
}

class CommonTableColumn {
  final String title;
  final String key;
  final int? flex;
  final TextAlign alignment;
  final CommonTableColumnStyle? style;

  const CommonTableColumn({
    required this.title,
    required this.key,
    this.flex,
    this.alignment = TextAlign.left,
    this.style,
  });
}

class CommonTableColumnStyle {
  final Color? textColor;
  final Color? backgroundColor;
  final bool showBadge;
  final Color? badgeColor;
  final Color? badgeTextColor;
  final double? badgeRadius;

  const CommonTableColumnStyle({
    this.textColor,
    this.backgroundColor,
    this.showBadge = false,
    this.badgeColor,
    this.badgeTextColor,
    this.badgeRadius = 15,
  });

  // Predefined styles for common use cases
  static const CommonTableColumnStyle yellowText = CommonTableColumnStyle(
    textColor: Color(0xFFFFD038),
  );

  static const CommonTableColumnStyle levelBadge = CommonTableColumnStyle(
    showBadge: true,
    badgeColor: Color(0xFF6F5502),
    badgeTextColor: Color(0xFFFFD038),
  );
}

extension CommonTableDataHelper on List<Map<String, dynamic>> {
  List<List<String>> toTableData(List<CommonTableColumn> columns) {
    return map((item) {
      return columns.map((column) {
        final value = item[column.key];
        return value?.toString() ?? '';
      }).toList();
    }).toList();
  }
}
